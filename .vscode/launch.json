{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "${workspaceFolder}/npm: compile"}, {"name": "Python: AI Agent Server", "type": "python", "request": "launch", "program": "${workspaceFolder}/ai_agent.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Test Connection", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_connection.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}]}