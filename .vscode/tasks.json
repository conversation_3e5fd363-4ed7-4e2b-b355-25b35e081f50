{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared", "reveal": "silent"}, "problemMatcher": "$tsc"}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"panel": "shared", "reveal": "never"}, "isBackground": true, "problemMatcher": "$tsc-watch"}, {"label": "Start AI Agent Server", "type": "shell", "command": "python3", "args": ["ai_agent.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Test Connection", "type": "shell", "command": "python3", "args": ["test_connection.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "problemMatcher": []}]}