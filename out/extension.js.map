{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,+DAA4D;AAC5D,6DAA0D;AAE1D,IAAI,aAAkC,CAAC;AACvC,IAAI,YAA4C,CAAC;AAEjD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAExD,gCAAgC;IAChC,aAAa,GAAG,IAAI,yCAAmB,CAAC,OAAO,CAAC,CAAC;IAEjD,oBAAoB;IACpB,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACzF,IAAI,CAAC,YAAY,EAAE;YACf,YAAY,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;SACjE;QACD,YAAY,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QAC7F,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,MAAM,EAAE,4CAA4C;YACpD,WAAW,EAAE,2BAA2B;SAC3C,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,YAAY,EAAE;gBACf,YAAY,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;aACjE;YACD,YAAY,CAAC,IAAI,EAAE,CAAC;YACpB,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACrC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvF,IAAI,YAAY,EAAE;YACd,YAAY,CAAC,MAAM,EAAE,CAAC;SACzB;aAAM;YACH,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;SAC3D;IACL,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,gBAAgB,GAAG,IAAI,sBAAsB,EAAE,CAAC;IACtD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE;QACxC,gBAAgB,EAAE,gBAAgB;QAClC,eAAe,EAAE,IAAI;KACxB,CAAC,CAAC;IAEH,4CAA4C;IAC5C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;IAE1E,2BAA2B;IAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;IAChE,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;QAC/B,aAAa,CAAC,WAAW,EAAE,CAAC;KAC/B;IAED,uBAAuB;IACvB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,aAAa,CAChB,CAAC;IAEF,uBAAuB;IACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+DAA+D,CAAC,CAAC;AAC1G,CAAC;AA/DD,4BA+DC;AAED,SAAgB,UAAU;IACtB,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;IACD,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;AACL,CAAC;AAPD,gCAOC;AAED,MAAM,sBAAsB;IAA5B;QACY,yBAAoB,GAAkE,IAAI,MAAM,CAAC,YAAY,EAA4C,CAAC;QACzJ,wBAAmB,GAA2D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAoB3H,CAAC;IAlBG,WAAW,CAAC,OAAuB;QAC/B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAwB;QAChC,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,IAAI,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,uBAAuB,CAAC;gBAC/F,IAAI,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,yBAAyB,CAAC;gBACnG,IAAI,cAAc,CAAC,eAAe,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC;aAC5E,CAAC,CAAC;SACN;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;CACJ;AAED,MAAM,cAAe,SAAQ,MAAM,CAAC,QAAQ;IACxC,YACoB,KAAa,EACb,gBAAiD,EACjD,OAAgB;QAEhC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAJf,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,YAAO,GAAP,OAAO,CAAS;QAIhC,IAAI,OAAO,EAAE;YACT,IAAI,CAAC,OAAO,GAAG;gBACX,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,KAAK;aACf,CAAC;SACL;QAED,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACrC,CAAC;CACJ"}