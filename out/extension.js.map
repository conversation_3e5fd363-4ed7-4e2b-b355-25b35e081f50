{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,+DAA4D;AAC5D,6DAA0D;AAE1D,IAAI,aAAkC,CAAC;AACvC,IAAI,YAA4C,CAAC;AAEjD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE3D,IAAI;QACA,gCAAgC;QAChC,aAAa,GAAG,IAAI,yCAAmB,CAAC,OAAO,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAE5C,oBAAoB;QACpB,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;YACzF,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAC/C,IAAI;gBACA,IAAI,CAAC,YAAY,EAAE;oBACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;oBAC7C,YAAY,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;iBACjE;gBACD,YAAY,CAAC,IAAI,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;aACxC;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;aACpE;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YAC7F,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,IAAI;gBACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC7C,MAAM,EAAE,4CAA4C;oBACpD,WAAW,EAAE,2BAA2B;iBAC3C,CAAC,CAAC;gBAEH,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;oBAC7C,IAAI,CAAC,YAAY,EAAE;wBACf,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;wBACzD,YAAY,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;qBACjE;oBACD,YAAY,CAAC,IAAI,EAAE,CAAC;oBACpB,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAClC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;iBAC5C;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;aACtE;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvF,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,IAAI;gBACA,IAAI,YAAY,EAAE;oBACd,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAC1C,YAAY,CAAC,MAAM,EAAE,CAAC;iBACzB;qBAAM;oBACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;oBACjD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;iBAC3D;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;aACtE;QACL,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,gBAAgB,GAAG,IAAI,sBAAsB,EAAE,CAAC;QACtD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE;YACxC,gBAAgB,EAAE,gBAAgB;YAClC,eAAe,EAAE,IAAI;SACxB,CAAC,CAAC;QAEH,4CAA4C;QAC5C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAExC,2BAA2B;QAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;YAC/B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAC1C,aAAa,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACtC,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,sEAAsE,CAAC,CAAC;YAC7G,CAAC,CAAC,CAAC;SACN;QAED,uBAAuB;QACvB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,gBAAgB,EAChB,kBAAkB,EAClB,kBAAkB,EAClB,aAAa,CAChB,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAE9D,uBAAuB;QACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kEAAkE,CAAC,CAAC;QACzG,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;KAEhE;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;KAClF;AACL,CAAC;AAvGD,4BAuGC;AAED,SAAgB,UAAU;IACtB,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;IACD,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;AACL,CAAC;AAPD,gCAOC;AAED,MAAM,sBAAsB;IAA5B;QACY,yBAAoB,GAAkE,IAAI,MAAM,CAAC,YAAY,EAA4C,CAAC;QACzJ,wBAAmB,GAA2D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAoB3H,CAAC;IAlBG,WAAW,CAAC,OAAuB;QAC/B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAwB;QAChC,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,IAAI,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,uBAAuB,CAAC;gBAC/F,IAAI,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,yBAAyB,CAAC;gBACnG,IAAI,cAAc,CAAC,eAAe,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC;aAC5E,CAAC,CAAC;SACN;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;CACJ;AAED,MAAM,cAAe,SAAQ,MAAM,CAAC,QAAQ;IACxC,YACoB,KAAa,EACb,gBAAiD,EACjD,SAAkB;QAElC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAJf,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,cAAS,GAAT,SAAS,CAAS;QAIlC,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,OAAO,GAAG;gBACX,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,KAAK;aACf,CAAC;SACL;IACL,CAAC;CACJ"}