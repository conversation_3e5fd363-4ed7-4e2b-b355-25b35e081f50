"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaWebviewPanel = void 0;
const vscode = require("vscode");
const path = require("path");
class OllamaWebviewPanel {
    constructor(context, agentProvider) {
        this.context = context;
        this.agentProvider = agentProvider;
        this.disposables = [];
    }
    show() {
        if (this.panel) {
            this.panel.reveal();
            return;
        }
        this.panel = vscode.window.createWebviewPanel('ollamaAgent', 'Ollama AI Agent', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.file(path.join(this.context.extensionPath, 'media'))
            ]
        });
        this.panel.webview.html = this.getWebviewContent();
        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'sendMessage':
                    await this.handleSendMessage(message.text);
                    break;
                case 'clearChat':
                    this.panel?.webview.postMessage({ command: 'clearChat' });
                    break;
            }
        }, null, this.disposables);
        // Handle panel disposal
        this.panel.onDidDispose(() => {
            this.panel = undefined;
        }, null, this.disposables);
    }
    toggle() {
        if (this.panel) {
            this.panel.dispose();
        }
        else {
            this.show();
        }
    }
    sendMessage(message) {
        if (this.panel) {
            this.panel.webview.postMessage({
                command: 'addMessage',
                message: message,
                sender: 'user'
            });
            this.handleSendMessage(message);
        }
    }
    async handleSendMessage(message) {
        try {
            // Show loading indicator
            this.panel?.webview.postMessage({
                command: 'showLoading'
            });
            // Send message to agent
            const response = await this.agentProvider.sendMessage(message);
            // Hide loading indicator and show response
            this.panel?.webview.postMessage({
                command: 'hideLoading'
            });
            this.panel?.webview.postMessage({
                command: 'addMessage',
                message: response,
                sender: 'agent'
            });
        }
        catch (error) {
            // Hide loading indicator and show error
            this.panel?.webview.postMessage({
                command: 'hideLoading'
            });
            this.panel?.webview.postMessage({
                command: 'addMessage',
                message: `Error: ${error}`,
                sender: 'error'
            });
            vscode.window.showErrorMessage(`Ollama Agent Error: ${error}`);
        }
    }
    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama AI Agent</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: auto;
            text-align: right;
        }
        
        .message.agent {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
        }
        
        .message.error {
            background-color: var(--vscode-errorBackground);
            color: var(--vscode-errorForeground);
            border: 1px solid var(--vscode-errorBorder);
        }
        
        .input-container {
            display: flex;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            padding: 10px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            font-family: inherit;
            font-size: inherit;
        }
        
        .send-button, .clear-button {
            padding: 10px 20px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        
        .send-button:hover, .clear-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .loading {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            margin: 10px 0;
        }
        
        .loading::after {
            content: '';
            animation: dots 1.5s steps(5, end) infinite;
        }
        
        @keyframes dots {
            0%, 20% { content: '.'; }
            40% { content: '..'; }
            60% { content: '...'; }
            80%, 100% { content: ''; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🤖 Ollama AI Agent</h2>
        <p>Connected to remote Ollama instance via SSH</p>
    </div>
    
    <div class="chat-container" id="chatContainer">
        <div class="message agent">
            <strong>Agent:</strong> Hello! I'm your Ollama AI assistant running on the remote server. How can I help you today?
        </div>
    </div>
    
    <div class="loading" id="loadingIndicator" style="display: none;">
        Agent is thinking
    </div>
    
    <div class="input-container">
        <input type="text" id="messageInput" class="message-input" placeholder="Type your message here..." />
        <button id="sendButton" class="send-button">Send</button>
        <button id="clearButton" class="clear-button">Clear</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const clearButton = document.getElementById('clearButton');
        const loadingIndicator = document.getElementById('loadingIndicator');

        function addMessage(message, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${sender}\`;
            
            const senderLabel = sender === 'user' ? 'You' : sender === 'agent' ? 'Agent' : 'Error';
            messageDiv.innerHTML = \`<strong>\${senderLabel}:</strong> \${message}\`;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                addMessage(message, 'user');
                vscode.postMessage({
                    command: 'sendMessage',
                    text: message
                });
                messageInput.value = '';
            }
        }

        function clearChat() {
            chatContainer.innerHTML = '<div class="message agent"><strong>Agent:</strong> Chat cleared. How can I help you?</div>';
        }

        sendButton.addEventListener('click', sendMessage);
        clearButton.addEventListener('click', () => {
            vscode.postMessage({ command: 'clearChat' });
            clearChat();
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Handle messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'addMessage':
                    addMessage(message.message, message.sender);
                    break;
                case 'showLoading':
                    loadingIndicator.style.display = 'block';
                    break;
                case 'hideLoading':
                    loadingIndicator.style.display = 'none';
                    break;
                case 'clearChat':
                    clearChat();
                    break;
            }
        });

        // Focus on input when panel opens
        messageInput.focus();
    </script>
</body>
</html>`;
    }
    dispose() {
        if (this.panel) {
            this.panel.dispose();
        }
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.OllamaWebviewPanel = OllamaWebviewPanel;
//# sourceMappingURL=ollamaWebviewPanel.js.map