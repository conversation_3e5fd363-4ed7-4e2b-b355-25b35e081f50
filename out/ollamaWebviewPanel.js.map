{"version": 3, "file": "ollamaWebviewPanel.js", "sourceRoot": "", "sources": ["../src/ollamaWebviewPanel.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6BAA6B;AAG7B,MAAa,kBAAkB;IAI3B,YACY,OAAgC,EAChC,aAAkC;QADlC,YAAO,GAAP,OAAO,CAAyB;QAChC,kBAAa,GAAb,aAAa,CAAqB;QAJtC,gBAAW,GAAwB,EAAE,CAAC;IAK3C,CAAC;IAEG,IAAI;QACP,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACzC,aAAa,EACb,iBAAiB,EACjB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;aAClE;SACJ,CACJ,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEnD,mCAAmC;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAClC,KAAK,EAAE,OAAO,EAAE,EAAE;YACd,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,aAAa;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC3C,MAAM;gBACV,KAAK,WAAW;oBACZ,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;oBAC1D,MAAM;aACb;QACL,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACnB,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,KAAK,CAAC,YAAY,CACnB,GAAG,EAAE;YACD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAC3B,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,WAAW,CACnB,CAAC;IACN,CAAC;IAEM,MAAM;QACT,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;aAAM;YACH,IAAI,CAAC,IAAI,EAAE,CAAC;SACf;IACL,CAAC;IAEM,WAAW,CAAC,OAAe;QAC9B,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;SACnC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC3C,IAAI;YACA,yBAAyB;YACzB,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,aAAa;aACzB,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE/D,2CAA2C;YAC3C,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,aAAa;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,QAAQ;gBACjB,MAAM,EAAE,OAAO;aAClB,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,wCAAwC;YACxC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,aAAa;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC;gBAC5B,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,UAAU,KAAK,EAAE;gBAC1B,MAAM,EAAE,OAAO;aAClB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;SAClE;IACL,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA2MP,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AAvUD,gDAuUC"}