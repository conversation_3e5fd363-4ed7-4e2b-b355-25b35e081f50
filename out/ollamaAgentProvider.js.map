{"version": 3, "file": "ollamaAgentProvider.js", "sourceRoot": "", "sources": ["../src/ollamaAgentProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,oCAAoC;AACpC,6BAA6B;AAC7B,iCAA0B;AAC1B,2BAA2B;AAE3B,MAAa,mBAAmB;IAM5B,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAF5C,oBAAe,GAAG,KAAK,CAAC;QAG5B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,WAAW;QACb,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QAED,IAAI;YACA,qCAAqC;YACrC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjD,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;gBAClF,OAAO,IAAI,CAAC;aACf;YAED,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAExE,0BAA0B;YAC1B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;YAE3E,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,EAAE;gBACnD,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;gBAC/B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3C,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3C,OAAO,CAAC,KAAK,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACpC,OAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI,EAAE,CAAC,CAAC;gBACvD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC7B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,uCAAuC;YACvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/C,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6CAA6C,CAAC,CAAC;gBACpF,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,CAAC,CAAC;gBACzE,OAAO,KAAK,CAAC;aAChB;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YAC/B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC9B;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1E,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe;QAC7B,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBACzC,IAAI,CAAC,OAAO,EAAE;oBACV,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;iBAC7C;aACJ;YAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,OAAO,EAAE;gBACxD,OAAO,EAAE,OAAO;aACnB,EAAE;gBACC,OAAO,EAAE,KAAK,CAAC,qCAAqC;aACvD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACvB,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;aACjC;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,eAAe,CAAC,CAAC;aAC3D;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACvD;IACL,CAAC;IAED,gBAAgB,CAAC,SAAoC,EAAE,OAA+B;QAClF,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SAC1B;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC;QAE/F,IAAI,CAAC,SAAS,GAAG,IAAI,YAAS,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE;YAClC,IAAI;gBACA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC7C,IAAI,QAAQ,CAAC,OAAO,EAAE;oBAClB,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBAChC;qBAAM;oBACH,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,eAAe,CAAC,CAAC,CAAC;iBACzD;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC,CAAC;aAC5D;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oBAAoB,CAAC,OAAe;QAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE;YAChE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;SACpD;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;AA3KD,kDA2KC"}