{"version": 3, "file": "ollamaAgentProvider.js", "sourceRoot": "", "sources": ["../src/ollamaAgentProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,kDAAoC;AACpC,2CAA6B;AAC7B,kDAA0B;AAC1B,4CAA2B;AAE3B,MAAa,mBAAmB;IAM5B,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAF5C,oBAAe,GAAG,KAAK,CAAC;QAG5B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,WAAW;QACb,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAElD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;SACf;QAED,IAAI;YACA,qCAAqC;YACrC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjD,IAAI,SAAS,EAAE;gBACX,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;gBAClF,OAAO,IAAI,CAAC;aACf;YAED,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBAClB,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;aAChB;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YAE3C,0BAA0B;YAC1B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;YAE3E,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,EAAE;gBACnD,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM;gBAC/B,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAE3E,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3C,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3C,OAAO,CAAC,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBACpC,OAAO,CAAC,GAAG,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;gBAC1D,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC7B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;gBAC/B,IAAI,IAAI,KAAK,CAAC,EAAE;oBACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,IAAI,EAAE,CAAC,CAAC;iBAClF;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACrC,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACzE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC7B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,oCAAoC;YACpC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,uCAAuC;YACvC,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/C,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6CAA6C,CAAC,CAAC;gBACpF,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC1C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6EAA6E,CAAC,CAAC;gBAC9G,OAAO,KAAK,CAAC;aAChB;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YAC/B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;SAChC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC9B;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1E,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe;QAC7B,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBACzC,IAAI,CAAC,OAAO,EAAE;oBACV,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;iBAC7C;aACJ;YAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,OAAO,EAAE;gBACxD,OAAO,EAAE,OAAO;aACnB,EAAE;gBACC,OAAO,EAAE,KAAK,CAAC,qCAAqC;aACvD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACvB,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;aACjC;iBAAM;gBACH,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,eAAe,CAAC,CAAC;aAC3D;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;SACvD;IACL,CAAC;IAED,gBAAgB,CAAC,SAAoC,EAAE,OAA+B;QAClF,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SAC1B;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC;QAE/F,IAAI,CAAC,SAAS,GAAG,IAAI,YAAS,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAS,EAAE,EAAE;YACvC,IAAI;gBACA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC7C,IAAI,QAAQ,CAAC,OAAO,EAAE;oBAClB,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBAChC;qBAAM;oBACH,OAAO,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,eAAe,CAAC,CAAC,CAAC;iBACzD;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC,CAAC;aAC5D;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oBAAoB,CAAC,OAAe;QAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE;YAChE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;SACpD;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;AAnMD,kDAmMC"}