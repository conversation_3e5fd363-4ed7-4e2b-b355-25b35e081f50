"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const ollamaAgentProvider_1 = require("./ollamaAgentProvider");
const ollamaWebviewPanel_1 = require("./ollamaWebviewPanel");
let agentProvider;
let webviewPanel;
function activate(context) {
    console.log('Ollama AI Agent extension is now active!');
    // Initialize the agent provider
    agentProvider = new ollamaAgentProvider_1.OllamaAgentProvider(context);
    // Register commands
    const startChatCommand = vscode.commands.registerCommand('ollamaAgent.startChat', async () => {
        if (!webviewPanel) {
            webviewPanel = new ollamaWebviewPanel_1.OllamaWebviewPanel(context, agentProvider);
        }
        webviewPanel.show();
    });
    const sendMessageCommand = vscode.commands.registerCommand('ollamaAgent.sendMessage', async () => {
        const message = await vscode.window.showInputBox({
            prompt: 'Enter your message for the Ollama AI agent',
            placeHolder: 'Type your message here...'
        });
        if (message) {
            if (!webviewPanel) {
                webviewPanel = new ollamaWebviewPanel_1.OllamaWebviewPanel(context, agentProvider);
            }
            webviewPanel.show();
            webviewPanel.sendMessage(message);
        }
    });
    const togglePanelCommand = vscode.commands.registerCommand('ollamaAgent.togglePanel', () => {
        if (webviewPanel) {
            webviewPanel.toggle();
        }
        else {
            vscode.commands.executeCommand('ollamaAgent.startChat');
        }
    });
    // Register tree data provider
    const treeDataProvider = new OllamaTreeDataProvider();
    vscode.window.createTreeView('ollamaAgent', {
        treeDataProvider: treeDataProvider,
        showCollapseAll: true
    });
    // Set context for when extension is enabled
    vscode.commands.executeCommand('setContext', 'ollamaAgent.enabled', true);
    // Auto-start if configured
    const config = vscode.workspace.getConfiguration('ollamaAgent');
    if (config.get('autoStart', true)) {
        agentProvider.startServer();
    }
    // Add to subscriptions
    context.subscriptions.push(startChatCommand, sendMessageCommand, togglePanelCommand, agentProvider);
    // Show welcome message
    vscode.window.showInformationMessage('Ollama AI Agent is ready! Use Ctrl+Shift+O to start chatting.');
}
exports.activate = activate;
function deactivate() {
    if (webviewPanel) {
        webviewPanel.dispose();
    }
    if (agentProvider) {
        agentProvider.dispose();
    }
}
exports.deactivate = deactivate;
class OllamaTreeDataProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve([
                new OllamaTreeItem('Start Chat', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.startChat'),
                new OllamaTreeItem('Send Message', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.sendMessage'),
                new OllamaTreeItem('Server Status', vscode.TreeItemCollapsibleState.None)
            ]);
        }
        return Promise.resolve([]);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
}
class OllamaTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, command) {
        super(label, collapsibleState);
        this.label = label;
        this.collapsibleState = collapsibleState;
        this.command = command;
        if (command) {
            this.command = {
                command: command,
                title: label
            };
        }
        this.tooltip = `${this.label}`;
        this.contextValue = 'ollamaItem';
    }
}
//# sourceMappingURL=extension.js.map