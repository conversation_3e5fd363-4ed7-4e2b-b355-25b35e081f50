"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const ollamaAgentProvider_1 = require("./ollamaAgentProvider");
const ollamaWebviewPanel_1 = require("./ollamaWebviewPanel");
let agentProvider;
let webviewPanel;
function activate(context) {
    console.log('🚀 Ollama AI Agent extension is now active!');
    try {
        // Initialize the agent provider
        agentProvider = new ollamaAgentProvider_1.OllamaAgentProvider(context);
        console.log('✅ Agent provider initialized');
        // Register commands
        const startChatCommand = vscode.commands.registerCommand('ollamaAgent.startChat', async () => {
            console.log('🎯 Start Chat command triggered');
            try {
                if (!webviewPanel) {
                    console.log('📱 Creating new webview panel');
                    webviewPanel = new ollamaWebviewPanel_1.OllamaWebviewPanel(context, agentProvider);
                }
                webviewPanel.show();
                console.log('✅ Webview panel shown');
            }
            catch (error) {
                console.error('❌ Error in startChat command:', error);
                vscode.window.showErrorMessage(`Failed to start chat: ${error}`);
            }
        });
        const sendMessageCommand = vscode.commands.registerCommand('ollamaAgent.sendMessage', async () => {
            console.log('💬 Send Message command triggered');
            try {
                const message = await vscode.window.showInputBox({
                    prompt: 'Enter your message for the Ollama AI agent',
                    placeHolder: 'Type your message here...'
                });
                if (message) {
                    console.log('📝 Message received:', message);
                    if (!webviewPanel) {
                        console.log('📱 Creating new webview panel for message');
                        webviewPanel = new ollamaWebviewPanel_1.OllamaWebviewPanel(context, agentProvider);
                    }
                    webviewPanel.show();
                    webviewPanel.sendMessage(message);
                    console.log('✅ Message sent to webview');
                }
            }
            catch (error) {
                console.error('❌ Error in sendMessage command:', error);
                vscode.window.showErrorMessage(`Failed to send message: ${error}`);
            }
        });
        const togglePanelCommand = vscode.commands.registerCommand('ollamaAgent.togglePanel', () => {
            console.log('🔄 Toggle Panel command triggered');
            try {
                if (webviewPanel) {
                    console.log('🔄 Toggling existing panel');
                    webviewPanel.toggle();
                }
                else {
                    console.log('🔄 No panel exists, starting chat');
                    vscode.commands.executeCommand('ollamaAgent.startChat');
                }
            }
            catch (error) {
                console.error('❌ Error in togglePanel command:', error);
                vscode.window.showErrorMessage(`Failed to toggle panel: ${error}`);
            }
        });
        // Register tree data provider
        console.log('🌳 Registering tree data provider');
        const treeDataProvider = new OllamaTreeDataProvider();
        vscode.window.createTreeView('ollamaAgent', {
            treeDataProvider: treeDataProvider,
            showCollapseAll: true
        });
        // Set context for when extension is enabled
        vscode.commands.executeCommand('setContext', 'ollamaAgent.enabled', true);
        console.log('🔧 Extension context set');
        // Auto-start if configured
        const config = vscode.workspace.getConfiguration('ollamaAgent');
        if (config.get('autoStart', true)) {
            console.log('🚀 Auto-starting server...');
            agentProvider.startServer().catch(error => {
                console.error('❌ Failed to auto-start server:', error);
                vscode.window.showWarningMessage('Failed to auto-start Ollama Agent server. You can start it manually.');
            });
        }
        // Add to subscriptions
        context.subscriptions.push(startChatCommand, sendMessageCommand, togglePanelCommand, agentProvider);
        console.log('📋 Commands registered and subscriptions added');
        // Show welcome message
        vscode.window.showInformationMessage('🤖 Ollama AI Agent is ready! Use Ctrl+Shift+O to start chatting.');
        console.log('✅ Extension activation completed successfully');
    }
    catch (error) {
        console.error('❌ Extension activation failed:', error);
        vscode.window.showErrorMessage(`Failed to activate Ollama AI Agent: ${error}`);
    }
}
exports.activate = activate;
function deactivate() {
    if (webviewPanel) {
        webviewPanel.dispose();
    }
    if (agentProvider) {
        agentProvider.dispose();
    }
}
exports.deactivate = deactivate;
class OllamaTreeDataProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve([
                new OllamaTreeItem('Start Chat', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.startChat'),
                new OllamaTreeItem('Send Message', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.sendMessage'),
                new OllamaTreeItem('Server Status', vscode.TreeItemCollapsibleState.None)
            ]);
        }
        return Promise.resolve([]);
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
}
class OllamaTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, commandId) {
        super(label, collapsibleState);
        this.label = label;
        this.collapsibleState = collapsibleState;
        this.commandId = commandId;
        if (commandId) {
            this.command = {
                command: commandId,
                title: label
            };
        }
    }
}
//# sourceMappingURL=extension.js.map