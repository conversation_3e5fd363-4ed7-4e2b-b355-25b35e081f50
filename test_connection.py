#!/usr/bin/env python3
"""
Test script to verify SSH connection and Ollama availability
"""

import paramiko
import os
import sys
from dotenv import load_dotenv

def test_ssh_connection():
    """Test SSH connection to remote machine"""
    load_dotenv()
    
    ssh_host = os.getenv('SSH_HOST')
    ssh_user = os.getenv('SSH_USER')
    ssh_port = int(os.getenv('SSH_PORT', 22))
    
    if not ssh_host or not ssh_user:
        print("❌ SSH configuration missing in .env file")
        return False
    
    print(f"🔗 Testing SSH connection to {ssh_user}@{ssh_host}:{ssh_port}")
    
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh_client.connect(
            hostname=ssh_host,
            username=ssh_user,
            port=ssh_port,
            timeout=10
        )
        
        print("✅ SSH connection successful!")
        
        # Test basic command
        stdin, stdout, stderr = ssh_client.exec_command('whoami')
        output = stdout.read().decode('utf-8').strip()
        print(f"✅ Remote user: {output}")
        
        ssh_client.close()
        return True
        
    except paramiko.AuthenticationException:
        print("❌ SSH authentication failed")
        print("💡 Make sure SSH key authentication is set up")
        print("   Try: ssh-copy-id {}@{}".format(ssh_user, ssh_host))
        return False
    except paramiko.SSHException as e:
        print(f"❌ SSH connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def test_ollama_availability():
    """Test if Ollama is available on remote machine"""
    load_dotenv()
    
    ssh_host = os.getenv('SSH_HOST')
    ssh_user = os.getenv('SSH_USER')
    ssh_port = int(os.getenv('SSH_PORT', 22))
    ollama_model = os.getenv('OLLAMA_MODEL', 'gemma3:1b')
    
    print(f"🤖 Testing Ollama availability (model: {ollama_model})")
    
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh_client.connect(
            hostname=ssh_host,
            username=ssh_user,
            port=ssh_port,
            timeout=10
        )
        
        # Check if ollama command exists
        stdin, stdout, stderr = ssh_client.exec_command('which ollama')
        if stdout.channel.recv_exit_status() != 0:
            print("❌ Ollama not found on remote machine")
            ssh_client.close()
            return False
        
        ollama_path = stdout.read().decode('utf-8').strip()
        print(f"✅ Ollama found at: {ollama_path}")
        
        # Check if model is available
        stdin, stdout, stderr = ssh_client.exec_command('ollama list')
        output = stdout.read().decode('utf-8')
        
        if ollama_model.split(':')[0] in output:
            print(f"✅ Model {ollama_model} is available")
        else:
            print(f"⚠️  Model {ollama_model} not found in available models")
            print("Available models:")
            print(output)
        
        ssh_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to test Ollama: {e}")
        return False

def test_simple_chat():
    """Test a simple chat interaction"""
    load_dotenv()
    
    ssh_host = os.getenv('SSH_HOST')
    ssh_user = os.getenv('SSH_USER')
    ssh_port = int(os.getenv('SSH_PORT', 22))
    ollama_command = os.getenv('OLLAMA_COMMAND', 'ollama run gemma3:1b')
    
    print("💬 Testing simple chat interaction...")
    
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh_client.connect(
            hostname=ssh_host,
            username=ssh_user,
            port=ssh_port,
            timeout=10
        )
        
        # Send a simple test message
        test_message = "Hello, can you respond with just 'Hello back!'?"
        command = f'echo "{test_message}" | {ollama_command}'
        
        print(f"Sending test message: {test_message}")
        stdin, stdout, stderr = ssh_client.exec_command(command, timeout=30)
        
        output = stdout.read().decode('utf-8').strip()
        error = stderr.read().decode('utf-8').strip()
        
        if output:
            print(f"✅ Ollama response: {output}")
        else:
            print(f"❌ No response from Ollama")
            if error:
                print(f"Error: {error}")
        
        ssh_client.close()
        return bool(output)
        
    except Exception as e:
        print(f"❌ Chat test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Ollama AI Agent Connection Test")
    print("=" * 40)
    
    # Test SSH connection
    if not test_ssh_connection():
        print("\n❌ SSH connection test failed. Please fix SSH configuration first.")
        sys.exit(1)
    
    print()
    
    # Test Ollama availability
    if not test_ollama_availability():
        print("\n❌ Ollama test failed. Please ensure Ollama is installed and the model is available.")
        sys.exit(1)
    
    print()
    
    # Test simple chat
    if not test_simple_chat():
        print("\n❌ Chat test failed. There might be an issue with the Ollama model.")
        sys.exit(1)
    
    print("\n🎉 All tests passed! Your setup is ready.")
    print("You can now start the AI agent server with: python3 start_agent.py")

if __name__ == "__main__":
    main()
