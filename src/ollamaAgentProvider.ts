import * as vscode from 'vscode';
import * as cp from 'child_process';
import * as path from 'path';
import axios from 'axios';
import WebSocket from 'ws';

export class OllamaAgentProvider implements vscode.Disposable {
    private serverProcess: cp.ChildProcess | undefined;
    private serverUrl: string;
    private websocket: WebSocket | undefined;
    private isServerRunning = false;

    constructor(private context: vscode.ExtensionContext) {
        const config = vscode.workspace.getConfiguration('ollamaAgent');
        this.serverUrl = config.get('serverUrl', 'http://localhost:8080');
    }

    async startServer(): Promise<boolean> {
        console.log('🚀 Starting Ollama Agent server...');

        if (this.isServerRunning) {
            console.log('✅ Server is already running');
            return true;
        }

        try {
            // Check if server is already running
            console.log('🔍 Checking if server is already running...');
            const isRunning = await this.checkServerStatus();
            if (isRunning) {
                this.isServerRunning = true;
                console.log('✅ Server is already running externally');
                vscode.window.showInformationMessage('Ollama AI Agent server is already running');
                return true;
            }

            // Find the Python script path
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                console.error('❌ No workspace folder found');
                vscode.window.showErrorMessage('No workspace folder found');
                return false;
            }

            const scriptPath = path.join(workspaceFolder.uri.fsPath, 'ai_agent.py');
            console.log('📁 Script path:', scriptPath);

            // Start the Python server
            console.log('🐍 Starting Python server...');
            vscode.window.showInformationMessage('Starting Ollama AI Agent server...');

            this.serverProcess = cp.spawn('python3', [scriptPath], {
                cwd: workspaceFolder.uri.fsPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            console.log('🔄 Server process spawned with PID:', this.serverProcess.pid);

            this.serverProcess.stdout?.on('data', (data) => {
                console.log(`📤 Server stdout: ${data}`);
            });

            this.serverProcess.stderr?.on('data', (data) => {
                console.error(`📥 Server stderr: ${data}`);
            });

            this.serverProcess.on('close', (code) => {
                console.log(`🔚 Server process exited with code ${code}`);
                this.isServerRunning = false;
                this.serverProcess = undefined;
                if (code !== 0) {
                    vscode.window.showErrorMessage(`Ollama Agent server exited with code ${code}`);
                }
            });

            this.serverProcess.on('error', (error) => {
                console.error('❌ Server process error:', error);
                vscode.window.showErrorMessage(`Server process error: ${error.message}`);
                this.isServerRunning = false;
                this.serverProcess = undefined;
            });

            // Wait a moment for server to start
            console.log('⏳ Waiting for server to start...');
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Check if server started successfully
            console.log('🔍 Checking server status...');
            const started = await this.checkServerStatus();
            if (started) {
                this.isServerRunning = true;
                console.log('✅ Server started successfully');
                vscode.window.showInformationMessage('Ollama AI Agent server started successfully');
                return true;
            } else {
                console.error('❌ Server failed to start');
                vscode.window.showErrorMessage('Failed to start Ollama AI Agent server. Check the output panel for details.');
                return false;
            }

        } catch (error) {
            vscode.window.showErrorMessage(`Error starting server: ${error}`);
            return false;
        }
    }

    async stopServer(): Promise<void> {
        if (this.serverProcess) {
            this.serverProcess.kill();
            this.serverProcess = undefined;
            this.isServerRunning = false;
        }
        
        if (this.websocket) {
            this.websocket.close();
            this.websocket = undefined;
        }
    }

    async checkServerStatus(): Promise<boolean> {
        try {
            const response = await axios.get(`${this.serverUrl}/`, { timeout: 5000 });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    async sendMessage(message: string): Promise<string> {
        try {
            if (!this.isServerRunning) {
                const started = await this.startServer();
                if (!started) {
                    throw new Error('Failed to start server');
                }
            }

            const response = await axios.post(`${this.serverUrl}/chat`, {
                message: message
            }, {
                timeout: 30000 // 30 second timeout for AI responses
            });

            if (response.data.success) {
                return response.data.response;
            } else {
                throw new Error(response.data.error || 'Unknown error');
            }

        } catch (error) {
            throw new Error(`Failed to send message: ${error}`);
        }
    }

    connectWebSocket(onMessage: (message: string) => void, onError: (error: Error) => void): void {
        if (this.websocket) {
            this.websocket.close();
        }

        const wsUrl = this.serverUrl.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws';
        
        this.websocket = new WebSocket(wsUrl);

        this.websocket.on('open', () => {
            console.log('WebSocket connected');
        });

        this.websocket.on('message', (data: any) => {
            try {
                const response = JSON.parse(data.toString());
                if (response.success) {
                    onMessage(response.response);
                } else {
                    onError(new Error(response.error || 'Unknown error'));
                }
            } catch (error) {
                onError(new Error(`Failed to parse response: ${error}`));
            }
        });

        this.websocket.on('error', (error: Error) => {
            onError(error);
        });

        this.websocket.on('close', () => {
            console.log('WebSocket disconnected');
            this.websocket = undefined;
        });
    }

    sendWebSocketMessage(message: string): void {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({ message }));
        } else {
            throw new Error('WebSocket not connected');
        }
    }

    dispose(): void {
        this.stopServer();
    }
}
