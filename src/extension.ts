import * as vscode from 'vscode';
import * as path from 'path';
import { OllamaAgentProvider } from './ollamaAgentProvider';
import { OllamaWebviewPanel } from './ollamaWebviewPanel';

let agentProvider: OllamaAgentProvider;
let webviewPanel: OllamaWebviewPanel | undefined;

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Ollama AI Agent extension is now active!');

    try {
        // Initialize the agent provider
        agentProvider = new OllamaAgentProvider(context);
        console.log('✅ Agent provider initialized');

        // Register commands
        const startChatCommand = vscode.commands.registerCommand('ollamaAgent.startChat', async () => {
            console.log('🎯 Start Chat command triggered');
            try {
                if (!webviewPanel) {
                    console.log('📱 Creating new webview panel');
                    webviewPanel = new OllamaWebviewPanel(context, agentProvider);
                }
                webviewPanel.show();
                console.log('✅ Webview panel shown');
            } catch (error) {
                console.error('❌ Error in startChat command:', error);
                vscode.window.showErrorMessage(`Failed to start chat: ${error}`);
            }
        });

        const sendMessageCommand = vscode.commands.registerCommand('ollamaAgent.sendMessage', async () => {
            console.log('💬 Send Message command triggered');
            try {
                const message = await vscode.window.showInputBox({
                    prompt: 'Enter your message for the Ollama AI agent',
                    placeHolder: 'Type your message here...'
                });

                if (message) {
                    console.log('📝 Message received:', message);
                    if (!webviewPanel) {
                        console.log('📱 Creating new webview panel for message');
                        webviewPanel = new OllamaWebviewPanel(context, agentProvider);
                    }
                    webviewPanel.show();
                    webviewPanel.sendMessage(message);
                    console.log('✅ Message sent to webview');
                }
            } catch (error) {
                console.error('❌ Error in sendMessage command:', error);
                vscode.window.showErrorMessage(`Failed to send message: ${error}`);
            }
        });

        const togglePanelCommand = vscode.commands.registerCommand('ollamaAgent.togglePanel', () => {
            console.log('🔄 Toggle Panel command triggered');
            try {
                if (webviewPanel) {
                    console.log('🔄 Toggling existing panel');
                    webviewPanel.toggle();
                } else {
                    console.log('🔄 No panel exists, starting chat');
                    vscode.commands.executeCommand('ollamaAgent.startChat');
                }
            } catch (error) {
                console.error('❌ Error in togglePanel command:', error);
                vscode.window.showErrorMessage(`Failed to toggle panel: ${error}`);
            }
        });

        // Register tree data provider
        console.log('🌳 Registering tree data provider');
        const treeDataProvider = new OllamaTreeDataProvider();
        vscode.window.createTreeView('ollamaAgent', {
            treeDataProvider: treeDataProvider,
            showCollapseAll: true
        });

        // Set context for when extension is enabled
        vscode.commands.executeCommand('setContext', 'ollamaAgent.enabled', true);
        console.log('🔧 Extension context set');

        // Auto-start if configured
        const config = vscode.workspace.getConfiguration('ollamaAgent');
        if (config.get('autoStart', true)) {
            console.log('🚀 Auto-starting server...');
            agentProvider.startServer().catch(error => {
                console.error('❌ Failed to auto-start server:', error);
                vscode.window.showWarningMessage('Failed to auto-start Ollama Agent server. You can start it manually.');
            });
        }

        // Add to subscriptions
        context.subscriptions.push(
            startChatCommand,
            sendMessageCommand,
            togglePanelCommand,
            agentProvider
        );
        console.log('📋 Commands registered and subscriptions added');

        // Show welcome message
        vscode.window.showInformationMessage('🤖 Ollama AI Agent is ready! Use Ctrl+Shift+O to start chatting.');
        console.log('✅ Extension activation completed successfully');

    } catch (error) {
        console.error('❌ Extension activation failed:', error);
        vscode.window.showErrorMessage(`Failed to activate Ollama AI Agent: ${error}`);
    }
}

export function deactivate() {
    if (webviewPanel) {
        webviewPanel.dispose();
    }
    if (agentProvider) {
        agentProvider.dispose();
    }
}

class OllamaTreeDataProvider implements vscode.TreeDataProvider<OllamaTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<OllamaTreeItem | undefined | null | void> = new vscode.EventEmitter<OllamaTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<OllamaTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    getTreeItem(element: OllamaTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: OllamaTreeItem): Thenable<OllamaTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new OllamaTreeItem('Start Chat', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.startChat'),
                new OllamaTreeItem('Send Message', vscode.TreeItemCollapsibleState.None, 'ollamaAgent.sendMessage'),
                new OllamaTreeItem('Server Status', vscode.TreeItemCollapsibleState.None)
            ]);
        }
        return Promise.resolve([]);
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }
}

class OllamaTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly commandId?: string
    ) {
        super(label, collapsibleState);
        
        if (commandId) {
            this.command = {
                command: commandId,
                title: label
            };
        }
    }
}
