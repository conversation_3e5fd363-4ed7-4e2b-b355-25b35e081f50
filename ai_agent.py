#!/usr/bin/env python3
"""
AI Agent Interface for VS Code Extension
Connects to remote machine via SSH and interacts with Ollama model
"""

import asyncio
import json
import logging
import os
import sys
from typing import Optional, Dict, Any
import paramiko
import socket
from dotenv import load_dotenv
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SSHOllamaAgent:
    """Main AI Agent class that handles SSH connections and Ollama interactions"""
    
    def __init__(self):
        self.ssh_host = os.getenv('SSH_HOST', '**********')
        self.ssh_user = os.getenv('SSH_USER', 'onesmus')
        self.ssh_port = int(os.getenv('SSH_PORT', 22))
        self.ollama_model = os.getenv('<PERSON>LLAMA_MODEL', 'gemma3:1b')
        self.ollama_command = os.getenv('OLLAMA_COMMAND', 'ollama run gemma3:1b')
        
        self.ssh_client: Optional[paramiko.SSHClient] = None
        self.is_connected = False
        
    async def connect_ssh(self) -> bool:
        """Establish SSH connection to remote machine"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            logger.info(f"Connecting to {self.ssh_user}@{self.ssh_host}:{self.ssh_port}")
            
            # Try to connect using SSH key first, then password if needed
            try:
                self.ssh_client.connect(
                    hostname=self.ssh_host,
                    username=self.ssh_user,
                    port=self.ssh_port,
                    timeout=10
                )
            except paramiko.AuthenticationException:
                logger.warning("SSH key authentication failed, you may need to set up SSH keys")
                return False
            except Exception as e:
                logger.error(f"SSH connection failed: {e}")
                return False
                
            self.is_connected = True
            logger.info("SSH connection established successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to establish SSH connection: {e}")
            return False
    
    async def disconnect_ssh(self):
        """Close SSH connection"""
        if self.ssh_client:
            self.ssh_client.close()
            self.is_connected = False
            logger.info("SSH connection closed")
    
    async def execute_command(self, command: str) -> Dict[str, Any]:
        """Execute command on remote machine via SSH"""
        if not self.is_connected or not self.ssh_client:
            return {"error": "Not connected to SSH server"}
        
        try:
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=30)
            
            # Read output
            output = stdout.read().decode('utf-8').strip()
            error = stderr.read().decode('utf-8').strip()
            exit_code = stdout.channel.recv_exit_status()
            
            return {
                "output": output,
                "error": error,
                "exit_code": exit_code,
                "success": exit_code == 0
            }
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {"error": str(e), "success": False}
    
    async def chat_with_ollama(self, message: str) -> Dict[str, Any]:
        """Send message to Ollama model and get response"""
        if not self.is_connected:
            await self.connect_ssh()
            if not self.is_connected:
                return {"error": "Failed to connect to SSH server"}
        
        # Escape the message for shell execution
        escaped_message = message.replace('"', '\\"').replace('`', '\\`')
        
        # Create the command to send to Ollama
        command = f'echo "{escaped_message}" | {self.ollama_command}'
        
        logger.info(f"Sending to Ollama: {message}")
        result = await self.execute_command(command)
        
        if result.get("success"):
            return {
                "response": result["output"],
                "success": True
            }
        else:
            return {
                "error": result.get("error", "Unknown error"),
                "success": False
            }

# Initialize the agent
agent = SSHOllamaAgent()

# FastAPI app for the web interface
app = FastAPI(title="AI Agent Interface", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    logger.info("Starting AI Agent Interface...")
    await agent.connect_ssh()

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    logger.info("Shutting down AI Agent Interface...")
    await agent.disconnect_ssh()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "status": "running",
        "connected": agent.is_connected,
        "model": agent.ollama_model
    }

@app.post("/chat")
async def chat_endpoint(request: Dict[str, Any]):
    """REST endpoint for chat interactions"""
    message = request.get("message", "")
    if not message:
        return {"error": "No message provided"}
    
    response = await agent.chat_with_ollama(message)
    return response

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time chat"""
    await websocket.accept()
    logger.info("WebSocket connection established")
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            message = message_data.get("message", "")
            if not message:
                await websocket.send_text(json.dumps({
                    "error": "No message provided"
                }))
                continue
            
            # Send message to Ollama and get response
            response = await agent.chat_with_ollama(message)
            
            # Send response back to client
            await websocket.send_text(json.dumps(response))
            
    except WebSocketDisconnect:
        logger.info("WebSocket connection closed")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close()

if __name__ == "__main__":
    # Get configuration from environment
    host = os.getenv('AGENT_HOST', 'localhost')
    port = int(os.getenv('AGENT_PORT', 8080))
    
    logger.info(f"Starting server on {host}:{port}")
    uvicorn.run(app, host=host, port=port, log_level="info")
