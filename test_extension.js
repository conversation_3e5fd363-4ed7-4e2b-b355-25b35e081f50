#!/usr/bin/env node

/**
 * Simple test script to verify the extension can be loaded
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Ollama AI Agent Extension');
console.log('=====================================');

// Check if compiled files exist
const outDir = path.join(__dirname, 'out');
const requiredFiles = [
    'extension.js',
    'ollamaAgentProvider.js',
    'ollamaWebviewPanel.js'
];

console.log('📁 Checking compiled files...');
let allFilesExist = true;

for (const file of requiredFiles) {
    const filePath = path.join(outDir, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file} exists`);
    } else {
        console.log(`❌ ${file} missing`);
        allFilesExist = false;
    }
}

// Check package.json
console.log('\n📦 Checking package.json...');
const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    console.log(`✅ Extension name: ${packageJson.name}`);
    console.log(`✅ Display name: ${packageJson.displayName}`);
    console.log(`✅ Version: ${packageJson.version}`);
    console.log(`✅ Main entry: ${packageJson.main}`);
    
    // Check commands
    if (packageJson.contributes && packageJson.contributes.commands) {
        console.log(`✅ Commands defined: ${packageJson.contributes.commands.length}`);
        packageJson.contributes.commands.forEach(cmd => {
            console.log(`   - ${cmd.command}: ${cmd.title}`);
        });
    }
    
    // Check keybindings
    if (packageJson.contributes && packageJson.contributes.keybindings) {
        console.log(`✅ Keybindings defined: ${packageJson.contributes.keybindings.length}`);
        packageJson.contributes.keybindings.forEach(kb => {
            console.log(`   - ${kb.key}: ${kb.command}`);
        });
    }
} else {
    console.log('❌ package.json missing');
    allFilesExist = false;
}

// Check Python backend
console.log('\n🐍 Checking Python backend...');
const pythonFiles = ['ai_agent.py', 'requirements.txt', '.env'];

for (const file of pythonFiles) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file} exists`);
    } else {
        console.log(`❌ ${file} missing`);
        allFilesExist = false;
    }
}

console.log('\n🎯 Test Summary');
console.log('================');

if (allFilesExist) {
    console.log('✅ All required files are present');
    console.log('🚀 Extension should be ready for testing');
    console.log('\nNext steps:');
    console.log('1. Press F5 in VS Code to launch Extension Development Host');
    console.log('2. Use Ctrl+Shift+O to start chatting');
    console.log('3. Or use Command Palette: "Start Ollama Chat"');
} else {
    console.log('❌ Some files are missing');
    console.log('🔧 Please run: npm run compile');
}
