#!/bin/bash

# Ollama AI Agent Setup Script

echo "🤖 Ollama AI Agent Setup"
echo "======================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3."
    exit 1
fi

echo "✅ pip3 found"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Python dependencies"
    exit 1
fi

echo "✅ Python dependencies installed"

# Check if Node.js is installed (for VS Code extension)
if command -v npm &> /dev/null; then
    echo "✅ Node.js found: $(node --version)"
    echo "📦 Installing VS Code extension dependencies..."
    npm install
    
    if [ $? -eq 0 ]; then
        echo "✅ VS Code extension dependencies installed"
        echo "🔨 Compiling TypeScript..."
        npm run compile
        
        if [ $? -eq 0 ]; then
            echo "✅ TypeScript compilation successful"
        else
            echo "⚠️  TypeScript compilation failed, but Python agent will still work"
        fi
    else
        echo "⚠️  Failed to install VS Code extension dependencies"
    fi
else
    echo "⚠️  Node.js not found. VS Code extension features will not be available."
    echo "   You can still use the Python agent directly."
fi

# Make scripts executable
chmod +x start_agent.py
chmod +x test_connection.py

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Configure your .env file with the correct SSH details"
echo "2. Test the connection: python3 test_connection.py"
echo "3. Start the agent: python3 start_agent.py"
echo "4. Install the VS Code extension (if Node.js is available)"
echo ""
echo "For VS Code extension:"
echo "- Press F5 to launch Extension Development Host"
echo "- Or package the extension: vsce package"
