# Ollama AI Agent VS Code Extension

A VS Code extension that provides an interface to interact with a remote Ollama AI model via SSH connection.

## Features

- 🚀 Connect to remote Ollama instance via SSH
- 💬 Interactive chat interface within VS Code
- ⚡ Real-time communication with AI agent
- 🔧 Configurable server settings
- 🎯 Easy-to-use commands and keybindings

## Prerequisites

1. **Python 3.7+** installed on your local machine
2. **SSH access** to the remote machine running Ollama
3. **Ollama** installed and running on the remote machine
4. **SSH key authentication** set up (recommended)

## Installation

1. Clone this repository to your workspace
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Install VS Code extension dependencies:
   ```bash
   npm install
   ```
4. Compile the TypeScript code:
   ```bash
   npm run compile
   ```

## Configuration

### Environment Variables

Edit the `.env` file in the root directory:

```env
# SSH Configuration
SSH_HOST=**********
SSH_USER=onesmus
SSH_PORT=22

# Ollama Model Configuration
OLLAMA_MODEL=gemma3:1b
OLLAMA_COMMAND=ollama run gemma3:1b

# Agent Configuration
AGENT_PORT=8080
AGENT_HOST=localhost
```

### VS Code Settings

You can configure the extension through VS Code settings:

- `ollamaAgent.serverUrl`: URL of the Ollama AI Agent server (default: http://localhost:8080)
- `ollamaAgent.autoStart`: Automatically start the agent server when VS Code starts (default: true)

## Usage

### Testing the Setup

Before using the extension, test your setup:

```bash
# Test SSH connection and Ollama
python3 test_connection.py

# Test extension files
node test_extension.js
```

### Starting the Extension

1. **Development Mode**: Press `F5` in VS Code to launch Extension Development Host
2. **Keyboard shortcut**: Press `Ctrl+Shift+O` (or `Cmd+Shift+O` on Mac) in the Extension Development Host
3. **Command Palette**: Use `Ctrl+Shift+P` and run "Start Ollama Chat"
4. **Automatic**: The extension will auto-start the Python server when activated

### Using the Chat Interface

1. The chat panel will open beside your editor
2. Type your message in the input field
3. Press Enter or click "Send" to send your message
4. The AI agent will respond through the SSH connection to your remote Ollama instance

### Available Commands

- `Ollama Agent: Start Chat` - Opens the chat interface
- `Ollama Agent: Send Message` - Quick message input dialog
- `Ollama Agent: Toggle Panel` - Show/hide the chat panel

## Architecture

```
VS Code Extension
       ↓
Python AI Agent (ai_agent.py)
       ↓
SSH Connection
       ↓
Remote Machine (**********)
       ↓
Ollama Model (gemma3:1b)
```

## Files Structure

```
├── .env                          # Environment configuration
├── ai_agent.py                   # Python backend server
├── requirements.txt              # Python dependencies
├── package.json                  # VS Code extension manifest
├── tsconfig.json                 # TypeScript configuration
└── src/
    ├── extension.ts              # Main extension entry point
    ├── ollamaAgentProvider.ts    # Agent communication logic
    └── ollamaWebviewPanel.ts     # Chat UI implementation
```

## Troubleshooting

### SSH Connection Issues

1. **Authentication Failed**: 
   - Ensure SSH key authentication is set up
   - Try connecting manually: `ssh onesmus@**********`

2. **Connection Timeout**:
   - Check if the remote host is reachable
   - Verify the SSH port (default: 22)

### Ollama Issues

1. **Model Not Found**:
   - Ensure Ollama is installed on the remote machine
   - Verify the model name in `.env` file
   - Run `ollama list` on remote machine to see available models

2. **Slow Responses**:
   - This is normal for AI models, especially on limited hardware
   - Consider using a smaller model for faster responses

### Extension Issues

1. **Server Won't Start**:
   - Check Python installation: `python3 --version`
   - Install dependencies: `pip install -r requirements.txt`
   - Check the VS Code output panel for error messages

## Development

To modify or extend the extension:

1. Make changes to the TypeScript files in `src/`
2. Compile: `npm run compile`
3. Press F5 in VS Code to launch a new Extension Development Host
4. Test your changes

### Debugging

- **Extension logs**: Open Developer Console (`Help > Toggle Developer Tools`)
- **Python logs**: Check the integrated terminal where the server runs
- **Test scripts**: Use `python3 test_connection.py` and `node test_extension.js`

### Common Issues

If the extension isn't working:

1. **Compilation errors**: Run `npm run compile` and fix TypeScript errors
2. **Python server issues**: Test with `python3 ai_agent.py`
3. **SSH problems**: Verify with `ssh onesmus@***********`
4. **Missing dependencies**: Run `pip install -r requirements.txt`

See [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for detailed solutions.

## Security Notes

- SSH key authentication is recommended over password authentication
- The extension runs a local server on localhost:8080 by default
- All communication with the remote machine is encrypted via SSH

## License

MIT License - see LICENSE file for details.
