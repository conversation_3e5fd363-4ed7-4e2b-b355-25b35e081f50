{"name": "ollama-ai-agent", "displayName": "Ollama AI Agent", "description": "VS Code extension to interact with remote Ollama AI agent via SSH", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning"], "activationEvents": ["onCommand:ollamaAgent.startChat", "onCommand:ollamaAgent.sendMessage"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ollamaAgent.startChat", "title": "<PERSON> <PERSON><PERSON>", "category": "Ollama Agent"}, {"command": "ollamaAgent.sendMessage", "title": "Send Message to Ollama", "category": "Ollama Agent"}, {"command": "ollamaAgent.togglePanel", "title": "Toggle O<PERSON>", "category": "Ollama Agent"}], "keybindings": [{"command": "ollamaAgent.startChat", "key": "ctrl+shift+o", "mac": "cmd+shift+o"}], "views": {"explorer": [{"id": "ollamaAgent", "name": "Ollama AI Agent", "when": "ollamaAgent.enabled"}]}, "configuration": {"title": "Ollama AI Agent", "properties": {"ollamaAgent.serverUrl": {"type": "string", "default": "http://localhost:8080", "description": "URL of the Ollama AI Agent server"}, "ollamaAgent.autoStart": {"type": "boolean", "default": true, "description": "Automatically start the agent server when VS Code starts"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"ws": "^8.14.2", "axios": "^1.6.0"}}