# Troubleshooting Guide

## VS Code Extension Issues

### Problem: F5 doesn't launch Extension Development Host

**Symptoms:**
- Pressing F5 does nothing
- No Extension Development Host window opens
- No error messages

**Solutions:**

1. **Check if TypeScript is compiled:**
   ```bash
   npm run compile
   ```

2. **Verify launch configuration:**
   - Open `.vscode/launch.json`
   - Ensure "Run Extension" configuration exists
   - Check that `outFiles` points to `${workspaceFolder}/out/**/*.js`

3. **Check VS Code workspace:**
   - Make sure you have the project opened as a workspace folder
   - Verify `package.json` exists in the root

4. **Manual launch:**
   - Open Command Palette (`Ctrl+Shift+P`)
   - Type "Debug: Start Debugging"
   - Select "Run Extension"

### Problem: Ctrl+Shift+O doesn't work

**Symptoms:**
- Keyboard shortcut doesn't trigger the command
- No chat panel opens
- No response from extension

**Solutions:**

1. **Check extension activation:**
   - Open Developer Console (`Help > Toggle Developer Tools`)
   - Look for activation messages starting with 🚀
   - Check for any error messages

2. **Verify command registration:**
   - Open Command Palette (`Ctrl+Shift+P`)
   - Type "Start Ollama Chat"
   - If command appears, keybinding issue; if not, registration issue

3. **Check keybinding conflicts:**
   - Go to `File > Preferences > Keyboard Shortcuts`
   - Search for `ctrl+shift+o`
   - Look for conflicts with other extensions

4. **Manual command execution:**
   - Use Command Palette: "Ollama Agent: Start Chat"

### Problem: Extension activates but commands don't work

**Symptoms:**
- Extension shows as activated
- Commands appear in Command Palette
- Commands fail when executed

**Solutions:**

1. **Check console output:**
   - Open Developer Console
   - Look for error messages with ❌ prefix
   - Check for Python server startup issues

2. **Verify Python backend:**
   ```bash
   python3 test_connection.py
   ```

3. **Check server status:**
   - Look for server startup messages in console
   - Verify port 8080 is available
   - Check if Python dependencies are installed

## Python Backend Issues

### Problem: Server won't start

**Symptoms:**
- "Failed to start server" error
- Python process exits immediately
- Connection refused errors

**Solutions:**

1. **Check Python installation:**
   ```bash
   python3 --version
   pip3 --version
   ```

2. **Install dependencies:**
   ```bash
   pip3 install -r requirements.txt
   ```

3. **Test manual startup:**
   ```bash
   python3 ai_agent.py
   ```

4. **Check port availability:**
   ```bash
   netstat -an | grep 8080
   ```

### Problem: SSH connection fails

**Symptoms:**
- "SSH connection failed" in logs
- Authentication errors
- Connection timeout

**Solutions:**

1. **Test SSH manually:**
   ```bash
   ssh onesmus@***********
   ```

2. **Check SSH key setup:**
   ```bash
   ssh-add -l
   ls ~/.ssh/
   ```

3. **Verify .env configuration:**
   - Check SSH_HOST, SSH_USER, SSH_PORT
   - Ensure values match your setup

4. **Test with password (temporary):**
   - Modify `ai_agent.py` to use password authentication
   - Only for testing - use keys in production

### Problem: Ollama not responding

**Symptoms:**
- SSH connects but Ollama commands fail
- "Model not found" errors
- Slow or no responses

**Solutions:**

1. **Test Ollama on remote machine:**
   ```bash
   ssh onesmus@***********
   ollama list
   ollama run gemma3:1b
   ```

2. **Check model availability:**
   ```bash
   ollama list | grep gemma3
   ```

3. **Pull model if missing:**
   ```bash
   ollama pull gemma3:1b
   ```

4. **Test model directly:**
   ```bash
   echo "Hello" | ollama run gemma3:1b
   ```

## General Debugging Steps

### Enable Verbose Logging

1. **VS Code Extension:**
   - Check Developer Console for detailed logs
   - Look for emoji prefixes (🚀, ✅, ❌, etc.)

2. **Python Backend:**
   - Run with debug logging:
     ```bash
     python3 -c "import logging; logging.basicConfig(level=logging.DEBUG); exec(open('ai_agent.py').read())"
     ```

### Test Components Individually

1. **Test SSH connection:**
   ```bash
   python3 test_connection.py
   ```

2. **Test extension compilation:**
   ```bash
   node test_extension.js
   ```

3. **Test Python server:**
   ```bash
   python3 ai_agent.py
   # In another terminal:
   curl http://localhost:8080/
   ```

### Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "No workspace folder found" | VS Code not opened in project folder | Open project as workspace |
| "Failed to start server" | Python/dependency issues | Check Python setup |
| "SSH connection failed" | Network/auth issues | Test SSH manually |
| "Model not found" | Ollama model missing | Pull model on remote machine |
| "WebSocket not connected" | Server not running | Start Python backend |

### Getting Help

If you're still having issues:

1. **Check console output** for detailed error messages
2. **Run test scripts** to isolate the problem
3. **Test each component** individually
4. **Check network connectivity** and SSH access
5. **Verify all dependencies** are installed

For additional support, include:
- Console output with error messages
- Results of `python3 test_connection.py`
- Results of `node test_extension.js`
- Your `.env` configuration (without sensitive data)
